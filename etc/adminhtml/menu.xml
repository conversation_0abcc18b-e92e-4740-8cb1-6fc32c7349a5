<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Webguru_LandingPageImport::menu" title="Landing Page Import" module="Webguru_LandingPageImport" sortOrder="10" resource="Magento_Backend::content"/>
        <add id="Webguru_LandingPageImport::import" title="Import Landing Pages" module="Webguru_LandingPageImport" sortOrder="20" parent="Webguru_LandingPageImport::menu" action="landingpageimport/import/index" resource="Magento_Backend::content"/>
    </menu>
</config>
